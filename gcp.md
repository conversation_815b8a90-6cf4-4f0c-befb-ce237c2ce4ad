PROJECT_ID=powerful-surf-469320-f1
REGION=us-west2


gcloud init
gcloud services enable artifactregistry.googleapis.com

gcloud auth configure-docker \
    us-west2-docker.pkg.dev

docker buildx build --platform linux/amd64 -t us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-app:latest . --push
# docker push us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-app:latest
OR
gcloud builds submit --tag us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-app:latest .


gcloud run deploy nuvineer-app-service \
    --image us-west2-docker.pkg.dev/powerful-surf-469320-f1/nuvineerapp/nuvineer-app:latest \
    --platform managed \
    --region us-west2 \
    --allow-unauthenticated