{"name": "archknow-companion", "displayName": "ArchKnow Companion", "version": "0.1.0", "description": "Extract architectural knowledge from GitHub PRs", "type": "module", "bin": {"archknow": "./bin/cli.js"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "analyze-pr": "node src/test-pr.js"}, "keywords": ["architecture", "knowledge", "github", "pull-request", "ADR"], "author": "", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@modelcontextprotocol/sdk": "^1.13.2", "@octokit/app": "^15.1.6", "@octokit/auth-app": "^7.2.1", "@octokit/auth-token": "^6.0.0", "@octokit/request-error": "^7.0.0", "@octokit/rest": "^21.1.1", "@octokit/webhooks-methods": "^6.0.0", "@pinecone-database/pinecone": "^5.1.1", "@radix-ui/react-slot": "^1.2.0", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.49.4", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "chalk": "^5.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "commander": "^11.1.0", "crypto": "^1.0.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "fs-extra": "^11.1.1", "gray-matter": "^4.0.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.522.0", "marked": "^9.1.5", "mermaid": "^11.9.0", "next": "^14.2.28", "next-auth": "^4.24.11", "openai": "^4.95.0", "ora": "^7.0.1", "react": "^18", "react-dom": "^18", "react-markdown": "^10.1.0", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "simple-git": "^3.28.0", "slugify": "^1.6.6", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/highlight.js": "^9.12.4", "@types/lodash": "^4.17.20", "@types/marked": "^5.0.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "dotenv": "^17.0.0", "eslint": "^8", "eslint-config-next": "14.1.0", "jest": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "^5"}, "engines": {"node": ">=18.0.0"}, "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "archknow.generateImplementationPlan", "title": "ArchKnow: Generate Implementation Plan (Legacy - from Phase3 Output)", "category": "ArchKnow"}, {"command": "archknow.generateImplementationPlanFromFinalizedDoc", "title": "ArchKnow: Generate Implementation Plan from Finalized Doc", "category": "ArchKnow"}, {"command": "archknow.showDecisionById", "title": "ArchKnow: Show Decision By ID", "category": "ArchKnow"}], "menus": {"editor/title": [{"command": "archknow.generateImplementationPlanFromFinalizedDoc", "when": "resourceLangId == markdown && resourcePath =~ /\\.archknow\\/design_docs\\/ready_for_implementation\\//", "group": "navigation"}]}, "configuration": {}}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}