{"name": "nuvineer-worker", "version": "1.0.0", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "echo \"Error: no test specified\" && exit 1", "format": "prettier --write .", "format:check": "prettier --check ."}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.3.1", "prettier": "^3.3.3", "tsx": "^4.20.5", "typescript": "^5.9.2"}, "dependencies": {"@anthropic-ai/sdk": "^0.63.0", "@octokit/rest": "^20.0.2", "@supabase/supabase-js": "^2.39.0", "chalk": "^5.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^5.1.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2"}}