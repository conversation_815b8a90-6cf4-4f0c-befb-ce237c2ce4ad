import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const config = {
  port: process.env.PORT || 3000,
  environment: process.env.NODE_ENV || 'development',

  // CORS configuration
  cors: {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
    credentials: true,
  },

  // Supabase configuration
  supabase: {
    url: process.env.SUPABASE_URL!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  },

  // Cron job security
  cronSecret: process.env.CRON_SECRET,

  // Request limits
  requestLimits: {
    json: '10mb',
    urlencoded: '10mb',
  },
};

export default config;
