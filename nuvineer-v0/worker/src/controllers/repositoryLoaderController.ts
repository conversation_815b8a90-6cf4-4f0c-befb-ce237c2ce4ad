import { Request, Response } from 'express';
import { LoaderResponse } from '../types';
import { getOctokit, getAuthToken, checkRateLimit, logger } from '../utils';
import * as repositoryLoaderService from '../services/repositoryLoaderService';

export async function handleRepositoryLoaderJob(
  req: Request,
  res: Response<LoaderResponse>
) {
  logger.info('[LoaderController] Handler invoked via cron.');

  // Check GitHub rate limit before starting any work
  try {
    const octokit = await getOctokit();
    const rateLimitStatus = await checkRateLimit(octokit, 200);
    if (rateLimitStatus.isLimited) {
      const message = `GitHub rate limit is low (${
        rateLimitStatus.remaining
      } remaining). Pausing job until ${rateLimitStatus.resetTime.toLocaleTimeString()}.`;
      logger.warn(`[LoaderController] ${message}`);
      return res.status(200).json({
        action: 'skipped_concurrency',
        message: message,
        details: { reason: 'RATE_LIMIT_PAUSE' },
      });
    }
  } catch (error) {
    logger.warn(
      `[LoaderController] Could not check rate limit, proceeding with caution:`,
      error
    );
  }

  const job = await repositoryLoaderService.getNextPendingJob();
  if (!job) {
    logger.info('[LoaderController] No pending jobs found to process.');
    return res.status(200).json({
      action: 'no_jobs',
      message: 'No pending repository loading jobs found.',
    });
  }

  console.log(
    `[LoaderController] Found pending job: ID ${job.id} for ${job.repository_slug}, Phase: ${job.current_phase}, Page: ${job.next_page_to_process}`
  );

  const isProcessing = await repositoryLoaderService.markJobAsProcessing(
    job.id
  );
  if (!isProcessing) {
    return res.status(500).json({
      action: 'error',
      message: `Failed to mark job ${job.id} as processing.`,
    });
  }

  logger.info(`[LoaderController] Marked job ${job.id} as 'processing'.`);

  const effectiveInstallationId =
    job.installation_id === 0 ? undefined : job.installation_id.toString();
  const authToken = await getAuthToken(effectiveInstallationId);
  let octokit;
  try {
    octokit = await getOctokit(effectiveInstallationId);
    logger.info(
      `[LoaderController] GitHub client created for installation ID: ${
        effectiveInstallationId || 'default'
      }`
    );
  } catch (error) {
    logger.error(`[LoaderController] Error creating GitHub client:`, error);
    return res.status(500).json({
      action: 'error',
      message: `Failed to create GitHub client: ${
        error instanceof Error ? error.message : String(error)
      }`,
    });
  }

  let batchResult;

  try {
    if (job.current_phase === 'prs') {
      batchResult = await repositoryLoaderService.processPrBatch(job, octokit);

      if (batchResult.success) {
        if (batchResult.hasMore) {
          await repositoryLoaderService.updateJobProgress(job.id, {
            next_page_to_process: job.next_page_to_process + 1,
          });
          const response: LoaderResponse = {
            action: 'processed_batch',
            message: `Processed PR batch for ${job.repository_slug}. Added ${batchResult.itemsAdded} PRs.`,
            details: {
              phase: 'prs',
              items_added: batchResult.itemsAdded,
              next_page: job.next_page_to_process + 1,
            },
          };
          logger.info(`[Repository Loader Controller] Job completed:`, response);
          return res.status(200).json(response);
        } else {
          await repositoryLoaderService.updateJobProgress(job.id, {
            current_phase: 'commits',
            next_page_to_process: 1,
          });
          const response: LoaderResponse = {
            action: 'switched_phase',
            message: `Completed PR loading for ${job.repository_slug}. Switching to commits phase.`,
            details: { phase: 'commits', items_added: batchResult.itemsAdded },
          };
          logger.info(`[Repository Loader Controller] Job completed:`, response);
          return res.status(200).json(response);
        }
      }
    } else if (job.current_phase === 'commits') {
      batchResult = await repositoryLoaderService.processCommitBatch(
        job,
        authToken
      );

      if (batchResult.success) {
        if (batchResult.hasMore) {
          await repositoryLoaderService.updateJobProgress(job.id, {
            next_page_to_process: job.next_page_to_process + 1,
          });
          const response: LoaderResponse = {
            action: 'processed_batch',
            message: `Processed commit batch for ${job.repository_slug}. Added ${batchResult.itemsAdded} commits.`,
            details: {
              phase: 'commits',
              items_added: batchResult.itemsAdded,
              next_page: job.next_page_to_process + 1,
            },
          };
          logger.info(`[Repository Loader Controller] Job completed:`, response);
          return res.status(200).json(response);
        } else {
          await repositoryLoaderService.updateJobProgress(job.id, {
            current_phase: 'completed',
            is_completed: true,
            status: 'completed',
          });
          const response: LoaderResponse = {
            action: 'completed_job',
            message: `Completed repository loading job for ${job.repository_slug}.`,
            details: {
              phase: 'completed',
              items_added: batchResult.itemsAdded,
            },
          };
          logger.info(
            `[Repository Loader Controller] Job completed:`,
            response
          );
          return res.status(200).json(response);
        }
      }
    }
  } catch (error) {
    console.error(`[LoaderController] Error processing job ${job.id}:`, error);
    await repositoryLoaderService.updateJobProgress(job.id, {
      status: 'failed',
      error_message: error instanceof Error ? error.message : String(error),
    });
    return res.status(500).json({
      action: 'error',
      message: `Error processing job: ${
        error instanceof Error ? error.message : String(error)
      }`,
    });
  }
}

export async function getRepositoryLoaderStatus(req: Request, res: Response) {
  try {
    const job = await repositoryLoaderService.getNextPendingJob();

    res.status(200).json({
      success: true,
      hasActiveJobs: job !== null,
      message:
        job !== null
          ? 'Repository loader has active jobs to process'
          : 'No active repository loading jobs',
    });
  } catch (error) {
    logger.error(
      '[Repository Loader Controller] Error checking status:',
      error
    );
    res.status(500).json({
      success: false,
      error: 'Error checking repository loader status',
      details: error instanceof Error ? error.message : String(error),
    });
  }
}
