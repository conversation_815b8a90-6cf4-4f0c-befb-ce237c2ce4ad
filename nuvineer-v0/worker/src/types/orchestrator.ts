// Orchestrator-related types for worker utilities

export interface PRContext {
  html_url: string;
  title: string;
  body: string | null;
  number: number | string;
  merged_at: string | null;
  user: { login: string } | string;
  is_pseudo_pr?: boolean;
  commit_sha?: string;
}

export interface CodeChange {
  filename: string;
  additions?: number;
  deletions?: number;
  patch?: string;
  sha?: string;
  status?: string;
}

export interface Comment {
  body: string;
  user: { login: string } | null;
  created_at: string;
}

export interface ProcessMergedPRResult {
  status: string;
  decisions?: any[];
  decisionsCount?: number;
  duration?: number;
  error?: string;
  reason?: string;
  errors?: any[];
}

export interface GetAllDecisionMetadataOptions {
  includeSuperseded?: boolean;
}

export interface DecisionMetadata {
  id?: string;
  title?: string;
  description?: string;
  repository_slug?: string;
  pr_number?: number | string;
  pr_url?: string;
  pr_merged_at?: number;
  extracted_at?: string;
  is_superseded?: boolean;
  [key: string]: any;
}

// Analysis and processing result types
export interface OrchestratorAnalysisResult {
  status:
    | 'completed_successfully'
    | 'completed_with_errors'
    | 'completed_no_decisions'
    | 'failed'
    | 'feedback_generated'
    | 'processing';
  decisions?: Array<{
    id?: string;
    title: string;
    description?: string;
    rationale?: string;
    implications?: string;
  }>;
  error?: string;
  reason?: string;
  feedback?: string;
  duration?: number;
}

export interface DecisionForAnalysis {
  id: string;
  pinecone_id: string;
  title: string;
  description: string;
  related_files: string[];
  domain_concepts: string[];
  implications: string;
  rationale: string;
  pr_number: number | string | undefined;
}
