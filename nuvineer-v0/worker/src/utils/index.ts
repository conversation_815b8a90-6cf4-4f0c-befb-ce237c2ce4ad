// Shared utilities and imports for the server endpoints
import config from '../config';

export { getOctokit, getAuthToken, checkRateLimit } from './github-auth';

export {
  getRecentCommits,
  getCommitDetail,
  createPseudoPrFromCommit,
  getRecentPRs,
  getPR,
  getPRFiles,
  getPRComments,
} from './github';

export {
  callLightweightLlmForSignificance,
  callClaudeLlmForJson,
  callClaudeLlmRaw,
} from './llmUtils';

export { getRepositoryNamespace } from './repositoryNamespace';

export { isDefinitelyNotArchitectural } from './heuristics';

export { logger } from './logger';

export {
  getRepositorySettings,
  createRepositorySettings,
  updateRepositorySettings,
  upsertRepositorySettings,
  deleteRepositorySettings,
  getAllRepositorySettings,
} from './repositorySettingsService';

export {
  processMergedPR,
  getAllDecisionMetadataForRepo,
  extractParamsFromNamespace,
  validateGitHubSignature,
  logProcessedPR,
} from './orchestratorUtils';

// Helper function to validate cron secret
export function validateCronSecret(req: any): boolean {
  if (config.cronSecret) {
    const providedSecret =
      req.headers['authorization']?.split(' ')[1] || req.query.secret;
    return providedSecret === config.cronSecret;
  }
  return true; // No secret required
}

// Re-export shared Supabase client
export { supabaseAdmin } from './supabaseClient';
