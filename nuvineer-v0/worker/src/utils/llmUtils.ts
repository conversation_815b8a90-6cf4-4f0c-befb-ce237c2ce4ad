import { Anthropic } from '@anthropic-ai/sdk';
import { FileContext, SignificanceResponse } from '../types';
import { logger } from './logger';

const anthropicApiKey = process.env.ANTHROPIC_API_KEY;
if (!anthropicApiKey) {
  throw new Error('ANTHROPIC_API_KEY not configured for llmUtils');
}
const anthropic = new Anthropic({ apiKey: anthropicApiKey });

/**
 * Attempts to clean and validate JSON string from LLM response
 * @param jsonString The raw JSON string from LLM
 * @returns Cleaned JSON string
 */
function cleanLlmJsonResponse(jsonString: string): string {
  logger.debug('[LLM JSON Debug] Original JSON string:', jsonString);

  // Remove any markdown code block markers if present
  jsonString = jsonString.replace(/```json\n?|\n?```/g, '');

  // Remove any BOM or hidden characters at the start
  jsonString = jsonString.replace(/^\uFEFF/, '');

  // Remove any trailing colons after the JSON object
  jsonString = jsonString.replace(/}\s*:/g, '}');

  // Remove any trailing commas in arrays and objects
  jsonString = jsonString.replace(/,(\s*[}\]])/g, '$1');

  // Fix any unescaped newlines within string values
  jsonString = jsonString.replace(/(?<!\\)\\n/g, '\\n');

  // Note: Skipping quote fixing as it was causing issues
  // logger.debug('[LLM JSON Debug] Skipping quote fixing to avoid parsing issues');

  // Ensure proper escaping of forward slashes in URLs
  jsonString = jsonString.replace(/(?<!\\)\//g, '\\/');

  // Remove any whitespace before/after the JSON object
  jsonString = jsonString.trim();

  // Ensure the string starts with { and ends with }
  if (!jsonString.startsWith('{') || !jsonString.endsWith('}')) {
    throw new Error('Invalid JSON: Must start with { and end with }');
  }

  // Log the first few characters where the error might be occurring
  return jsonString;
}

/**
 * Extracts JSON from LLM response text more robustly
 * @param responseText The raw response text from LLM
 * @returns Extracted JSON string
 */
function extractJsonFromLlmResponse(responseText: string): string {
  // First try to find JSON within code blocks
  const codeBlockMatch = responseText.match(/```(?:json)?\n([\s\S]*?)\n```/);
  if (codeBlockMatch) {
    logger.debug('[LLM JSON Debug] Found JSON in code block');
    return codeBlockMatch[1].trim();
  }

  // Try to find JSON between curly braces
  const firstBrace = responseText.indexOf('{');
  const lastBrace = responseText.lastIndexOf('}');

  if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
    return responseText.substring(firstBrace, lastBrace + 1);
  }

  // If no clear JSON structure found, try to clean up the text
  return responseText.trim();
}

/**
 * Calls the Anthropic Claude API and expects a JSON response.
 * Attempts to parse the JSON from the response text.
 *
 * @param prompt - The prompt to send.
 * @param model - The Anthropic model to use.
 * @returns The parsed JSON object.
 * @throws {Error} If the API call fails or the response is not valid JSON.
 */
export async function callClaudeLlmForJson(
  prompt: string,
  model: string = 'claude-sonnet-4-20250514'
): Promise<any> {
  const anthropic = new Anthropic({
    apiKey: process.env.ANTHROPIC_API_KEY,
  });

  try {
    const completion = await anthropic.messages.create({
      model: model,
      max_tokens: 4096,
      temperature: 0.1,
      system:
        'You are a helpful AI that always responds with valid, parseable JSON. Your responses must:\n1. Start with an opening curly brace { and end with a closing curly brace }\n2. Use double quotes for all property names and string values\n3. Properly escape special characters in strings (quotes, newlines, etc.)\n4. Not include any text or formatting outside the JSON object\n5. Not include trailing commas in arrays or objects\n6. Keep string values concise and avoid redundancy\n7. Properly escape forward slashes in URLs\n8. Not include any markdown formatting or code block markers',
      messages: [{ role: 'user', content: prompt }],
    });

    const responseContent =
      completion.content[0].type === 'text' ? completion.content[0].text : '';

    if (!responseContent) {
      throw new Error('No text content in LLM response');
    }

    logger.debug('[LLM JSON Debug] Raw response content:', responseContent);

    // Extract JSON from response
    const jsonString = extractJsonFromLlmResponse(responseContent);

    // Clean and validate the JSON response
    const cleanedJson = cleanLlmJsonResponse(jsonString);

    try {
      // Try parsing with more detailed error handling
      const parsedJson = JSON.parse(cleanedJson);
      return parsedJson;
    } catch (parseError: unknown) {
      const errorMessage =
        parseError instanceof Error
          ? parseError.message
          : 'Unknown JSON parsing error';

      // Log the first few characters where the error occurred
      if (parseError instanceof SyntaxError && 'message' in parseError) {
        const errorPos = parseError.message.match(/position (\d+)/);
        if (errorPos && errorPos[1]) {
          const pos = parseInt(errorPos[1]);
          const context = cleanedJson.substring(
            Math.max(0, pos - 20),
            Math.min(cleanedJson.length, pos + 20)
          );
          logger.error(
            `[LLM JSON Debug] Error context (around position ${pos}):`,
            context
          );
        }
      }

      throw new Error(`Failed to parse LLM JSON response: ${errorMessage}`);
    }
  } catch (error: any) {
    throw new Error(`Failed to get valid JSON from LLM: ${error.message}`);
  }
}

// Add other LLM related utilities if needed, e.g., for non-JSON responses
export async function callClaudeLlmRaw(
  prompt: string,
  model?: string
): Promise<string> {
  const callId = `llm-raw-${Date.now()}`;
  const effectiveModel =
    model || process.env.ANTHROPIC_MODEL || 'claude-sonnet-4-20250514';
  logger.debug(
    `[${callId}] [LLM Request] Calling Anthropic for raw text with model: ${effectiveModel}`
  );

  try {
    const response = await anthropic.messages.create({
      model: effectiveModel,
      max_tokens: 4096,
      temperature: 0.1,
      messages: [{ role: 'user', content: prompt }],
    });
    const responseText =
      response.content[0]?.type === 'text' ? response.content[0].text : '';
    logger.debug(`[${callId}] [LLM Response] Raw response received.`);
    return responseText;
  } catch (error: any) {
    logger.error(
      `[${callId}] Error calling Anthropic API for raw text:`,
      error
    );
    throw new Error(`Anthropic API call for raw text failed: ${error.message}`);
  }
}

/**
 * A specialized function to call a lightweight LLM (like Claude Haiku) for a simple
 * architectural significance assessment.
 *
 * This function is designed to be fast and cheap, returning a simple binary answer.
 * @param commitDetails - A concise string of commit details (e.g., title and body).
 * @param fileContext - Optional file context including file names, directories, and change stats.
 * @param model - The LLM model to use, defaulting to a fast and cheap option.
 * @returns A promise that resolves to an object like `{ sig: 1 }` or `{ sig: 0 }`.
 */
export async function callLightweightLlmForSignificance(
  commitDetails: string,
  fileContext?: FileContext,
  model: string = 'claude-3-haiku-20240307'
): Promise<SignificanceResponse> {
  try {
    const anthropic = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
    let prompt = `Based on the following commit details, is this commit architecturally significant? Respond with only { "sig": 1 } for yes or { "sig": 0 } for no.\n\nCommit details:\n${commitDetails}`;

    if (fileContext) {
      prompt += `\n\nFile context:\n- Files changed: ${
        fileContext.filesChanged
      }\n- Total additions: ${fileContext.totalAdditions}\n- Total deletions: ${
        fileContext.totalDeletions
      }\n- Directories affected: ${
        fileContext.directories.join(', ') || 'N/A'
      }\n- File types: ${fileContext.fileTypes.join(', ')}`;
    }

    // logger.debug(`[LLM Significance] Sending prompt to ${model}:`, prompt); // Removed for cleaner logs

    const response = await anthropic.messages.create({
      model: model,
      max_tokens: 50,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.1,
    });

    const responseText =
      response.content[0]?.type === 'text' ? response.content[0].text : '';
    logger.debug(`[LLM Lightweight Check] Raw LLM response: "${responseText}"`);

    // Parse the JSON response
    const jsonMatch = responseText.match(/\{[^}]*\}/);
    if (!jsonMatch) {
      logger.error(
        `[LLM Lightweight Check] No JSON found in response: "${responseText}"`
      );
      throw new Error('Invalid response format from LLM');
    }

    const parsed = JSON.parse(jsonMatch[0]);
    logger.debug(`[LLM Lightweight Check] Parsed result:`, parsed);

    if (
      typeof parsed.sig !== 'number' ||
      (parsed.sig !== 0 && parsed.sig !== 1)
    ) {
      logger.error(`[LLM Lightweight Check] Invalid sig value:`, parsed.sig);
      throw new Error('Invalid sig value in LLM response');
    }

    logger.debug(
      `[LLM Lightweight Check] Final assessment: ${
        parsed.sig === 1 ? 'ARCHITECTURALLY SIGNIFICANT' : 'NOT SIGNIFICANT'
      }`
    );
    return { sig: parsed.sig as 0 | 1 };
  } catch (error) {
    logger.error(`[LLM Lightweight Check] Error during LLM call:`, error);
    logger.error(
      `[LLM Lightweight Check] Model: ${model}, Prompt length: ${commitDetails.length}`
    );
    throw error;
  }
}
